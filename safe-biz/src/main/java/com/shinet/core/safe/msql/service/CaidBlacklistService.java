package com.shinet.core.safe.msql.service;

import com.shinet.core.safe.core.dto.CaidRequestDTO;
import com.shinet.core.safe.core.entity.LockDeviceBlack;
import com.shinet.core.safe.core.service.CaidService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * CAID拉黑服务
 * 根据设备信息获取CAID并进行设备拉黑
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CaidBlacklistService {

    @Resource
    private CaidService caidService;

    @Resource
    private DeviceBlackService deviceBlackService;

    /**
     * 处理CAID拉黑流程
     * 异步执行：获取CAID → 设备拉黑
     *
     * @param request CAID请求参数
     * @param remark  拉黑原因备注
     */
//    @Async(DeviceRetryConstants.DATA_PERSISTENCE_EXECUTOR)
    public void processCaidBlacklist(CaidRequestDTO request, String remark) {
        if (request == null) {
            log.warn("CAID拉黑处理：请求参数为空，跳过处理");
            return;
        }

        try {
            log.info("开始处理CAID拉黑：deviceName={}, product={}",
                    request.getDeviceName(), request.getProduct());

            // 1. 调用CAID获取服务
            String caid = caidService.getCaid(request);

            if (StringUtils.isBlank(caid)) {
                log.warn("CAID拉黑处理：未获取到CAID，跳过拉黑操作，deviceName={}, product={}",
                        request.getDeviceName(), request.getProduct());
                return;
            }

            log.info("成功获取CAID：{}, 开始执行拉黑操作", caid);

            // 2. 执行设备拉黑
            int blacklistCount = deviceBlackService.batchAddBlackDevices(
                    LockDeviceBlack.OS_IOS,           // iOS设备
                    LockDeviceBlack.TARGET_TYPE_CAID, // CAID类型
                    caid,                             // CAID值
                    remark != null ? remark : "CAID自动拉黑"
            );

        } catch (Exception e) {
            log.error("CAID拉黑处理异常：deviceName={}, product={}, error={}",
                    request.getDeviceName(), request.getProduct(), e.getMessage(), e);
        }
    }
}
